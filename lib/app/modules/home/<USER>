import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'home_controller.dart';

class HomeView extends GetView<HomeController> {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 768;
    final isDesktop = screenSize.width > 1024;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0175C2), // Primary blue
              Color(0xFF1E3A8A), // Darker blue
              Color(0xFF312E81), // Deep purple-blue
            ],
          ),
        ),
        child: Safe<PERSON>rea(
          child: Column(
            children: [
              // Settings button in top left
              Padding(
                padding: EdgeInsets.all(isDesktop ? 24.0 : 16.0),
                child: Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: IconButton(
                        onPressed: () => Get.toNamed('/settings'),
                        icon: const Icon(
                          Icons.settings,
                          color: Colors.white,
                          size: 24,
                        ),
                        tooltip: 'الإعدادات',
                      ),
                    ),
                  ],
                ),
              ),

              // Main content
              Expanded(
                child: Center(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: isDesktop ? 48.0 : (isTablet ? 32.0 : 24.0),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            'assets/images/main.png',
                            width: isDesktop ? 300 : (isTablet ? 200 : 150),
                          ),
                          // Game title with Jeopardy-style design
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: isDesktop
                                  ? 48.0
                                  : (isTablet ? 36.0 : 24.0),
                              vertical: isDesktop
                                  ? 32.0
                                  : (isTablet ? 24.0 : 20.0),
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.3),
                                  blurRadius: 20,
                                  offset: const Offset(0, 8),
                                ),
                              ],
                              border: Border.all(
                                color: const Color(0xFFFFD700), // Gold border
                                width: 3,
                              ),
                            ),
                            child: Text(
                              'الفريق الذكي',
                              style: TextStyle(
                                fontSize: isDesktop ? 48 : (isTablet ? 36 : 28),
                                fontWeight: FontWeight.bold,
                                color: const Color(0xFF1E3A8A),
                                letterSpacing: 2,
                                shadows: [
                                  Shadow(
                                    color: Colors.grey.withOpacity(0.5),
                                    offset: const Offset(2, 2),
                                    blurRadius: 4,
                                  ),
                                ],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),

                          SizedBox(
                            height: isDesktop ? 64 : (isTablet ? 48 : 40),
                          ),

                          // Game buttons
                          ConstrainedBox(
                            constraints: BoxConstraints(
                              maxWidth: isDesktop
                                  ? 500
                                  : (isTablet ? 400 : double.infinity),
                            ),
                            child: Column(
                              children: [
                                // Start Private Room button
                                _buildGameButton(
                                  context: context,
                                  text: 'إنشاء غرفة خاصة',
                                  icon: Icons.group_add,
                                  onPressed: () {
                                    // TODO: Navigate to private room creation
                                    Get.snackbar(
                                      'قريباً',
                                      'سيتم إضافة هذه الميزة قريباً',
                                      backgroundColor: Colors.white.withOpacity(
                                        0.9,
                                      ),
                                      colorText: const Color(0xFF1E3A8A),
                                      snackPosition: SnackPosition.TOP,
                                    );
                                  },
                                  isDesktop: isDesktop,
                                  isTablet: isTablet,
                                ),

                                SizedBox(
                                  height: isDesktop ? 24 : (isTablet ? 20 : 16),
                                ),

                                // Play Online button
                                _buildGameButton(
                                  context: context,
                                  text: 'اللعب عبر الإنترنت',
                                  icon: Icons.public,
                                  onPressed: () {
                                    // TODO: Navigate to online game
                                    Get.snackbar(
                                      'قريباً',
                                      'سيتم إضافة هذه الميزة قريباً',
                                      backgroundColor: Colors.white.withOpacity(
                                        0.9,
                                      ),
                                      colorText: const Color(0xFF1E3A8A),
                                      snackPosition: SnackPosition.TOP,
                                    );
                                  },
                                  isDesktop: isDesktop,
                                  isTablet: isTablet,
                                ),
                              ],
                            ),
                          ),

                          SizedBox(
                            height: isDesktop ? 48 : (isTablet ? 36 : 24),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGameButton({
    required BuildContext context,
    required String text,
    required IconData icon,
    required VoidCallback onPressed,
    required bool isDesktop,
    required bool isTablet,
  }) {
    return Container(
      width: double.infinity,
      height: isDesktop ? 80 : (isTablet ? 70 : 60),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: const LinearGradient(
          colors: [
            Color(0xFFFFD700), // Gold
            Color(0xFFFFA500), // Orange
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: isDesktop ? 32 : (isTablet ? 24 : 20),
              vertical: isDesktop ? 20 : (isTablet ? 16 : 12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: const Color(0xFF1E3A8A),
                  size: isDesktop ? 32 : (isTablet ? 28 : 24),
                ),
                SizedBox(width: isDesktop ? 16 : (isTablet ? 12 : 8)),
                Text(
                  text,
                  style: TextStyle(
                    fontSize: isDesktop ? 24 : (isTablet ? 20 : 18),
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF1E3A8A),
                    letterSpacing: 1,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

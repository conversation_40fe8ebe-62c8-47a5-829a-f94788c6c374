import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'game_controller.dart';

class GameView extends GetView<GameController> {
  const GameView({super.key});

  // Sample categories in Arabic
  final List<String> categories = const [
    'التاريخ',
    'العلوم',
    'الرياضة',
    'الجغرافيا',
    'الأدب',
    'التكنولوجيا',
  ];

  // Score values for each category
  final List<int> scoreValues = const [100, 200, 300, 400, 500];

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 768;
    final isDesktop = screenSize.width > 1024;
    final isLandscape = screenSize.width > screenSize.height;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0F1B3C), // Dark navy
              Color(0xFF1E3A8A), // Blue
              Color(0xFF312E81), // Purple-blue
            ],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              // Header with back button and title
              Padding(
                padding: EdgeInsets.all(isDesktop ? 24.0 : 16.0),
                child: Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: IconButton(
                        onPressed: () => Get.back(),
                        icon: const Icon(
                          Icons.arrow_back,
                          color: Colors.white,
                          size: 24,
                        ),
                        tooltip: 'العودة',
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'الفريق الذكي',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: isDesktop ? 32 : (isTablet ? 28 : 24),
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFFFFD700),
                          letterSpacing: 2,
                          shadows: [
                            Shadow(
                              color: Colors.black.withOpacity(0.5),
                              offset: const Offset(2, 2),
                              blurRadius: 4,
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      width: isDesktop ? 64 : 48,
                    ), // Balance the back button
                  ],
                ),
              ),

              // Jeopardy Board
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: isDesktop ? 32.0 : (isTablet ? 24.0 : 16.0),
                    vertical: isDesktop ? 24.0 : (isTablet ? 20.0 : 16.0),
                  ),
                  child: isLandscape
                      ? // Landscape: All 6 categories in one row
                        _buildCategoryRow(
                          context,
                          categories,
                          isDesktop,
                          isTablet,
                        )
                      : // Portrait: 2 rows of 3 categories each
                        Column(
                          children: [
                            // Top 3 categories
                            Expanded(
                              child: _buildCategoryRow(
                                context,
                                categories.sublist(0, 3),
                                isDesktop,
                                isTablet,
                              ),
                            ),

                            SizedBox(
                              height: isDesktop ? 24 : (isTablet ? 20 : 16),
                            ),

                            // Bottom 3 categories
                            Expanded(
                              child: _buildCategoryRow(
                                context,
                                categories.sublist(3, 6),
                                isDesktop,
                                isTablet,
                              ),
                            ),
                          ],
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryRow(
    BuildContext context,
    List<String> rowCategories,
    bool isDesktop,
    bool isTablet,
  ) {
    return Row(
      children: rowCategories.map((category) {
        return Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: isDesktop ? 8.0 : (isTablet ? 6.0 : 4.0),
            ),
            child: _buildCategoryColumn(context, category, isDesktop, isTablet),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCategoryColumn(
    BuildContext context,
    String category,
    bool isDesktop,
    bool isTablet,
  ) {
    return Column(
      children: [
        // Category header
        Container(
          width: double.infinity,
          height: isDesktop ? 80 : (isTablet ? 70 : 60),
          decoration: BoxDecoration(
            color: const Color(0xFF1E40AF),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: const Color(0xFFFFD700), width: 2),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Center(
            child: Text(
              category,
              style: TextStyle(
                fontSize: isDesktop ? 18 : (isTablet ? 16 : 14),
                fontWeight: FontWeight.bold,
                color: Colors.white,
                letterSpacing: 1,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),

        SizedBox(height: isDesktop ? 12 : (isTablet ? 10 : 8)),

        // Score buttons
        ...scoreValues.map((score) {
          return Padding(
            padding: EdgeInsets.only(
              bottom: isDesktop ? 12.0 : (isTablet ? 10.0 : 8.0),
            ),
            child: _buildScoreButton(
              context,
              score,
              category,
              isDesktop,
              isTablet,
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildScoreButton(
    BuildContext context,
    int score,
    String category,
    bool isDesktop,
    bool isTablet,
  ) {
    return Container(
      width: double.infinity,
      height: isDesktop ? 60 : (isTablet ? 55 : 50),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color(0xFF1E40AF), // Blue
            Color(0xFF1E3A8A), // Darker blue
          ],
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFFFD700), width: 1.5),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            // TODO: Handle question selection
            Get.snackbar(
              'تم اختيار السؤال',
              'الفئة: $category - النقاط: $score',
              backgroundColor: const Color(0xFFFFD700),
              colorText: const Color(0xFF1E3A8A),
              snackPosition: SnackPosition.TOP,
              duration: const Duration(seconds: 2),
            );
          },
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: isDesktop ? 16 : (isTablet ? 12 : 8),
              vertical: isDesktop ? 12 : (isTablet ? 10 : 8),
            ),
            child: Center(
              child: Text(
                '$score',
                style: TextStyle(
                  fontSize: isDesktop ? 24 : (isTablet ? 20 : 18),
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFFFFD700),
                  letterSpacing: 1,
                  shadows: [
                    Shadow(
                      color: Colors.black.withOpacity(0.5),
                      offset: const Offset(1, 1),
                      blurRadius: 2,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
